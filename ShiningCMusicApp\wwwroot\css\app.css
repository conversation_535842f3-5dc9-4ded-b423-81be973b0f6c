html, body {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.logo {
    max-width: 800px;
    height: auto;
}

.logo-login {
    max-width: 350px;
    height: auto;
}

h1:focus {
    outline: none;
}

a, .btn-link {
    color: #0071c1;
}

.btn-primary {
    color: #fff;
    background-color: #1b6ec2;
    border-color: #1861ac;
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
    box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

.content {
    padding-top: 1.1rem;
}

.valid.modified:not([type=checkbox]) {
    outline: 1px solid #26b050;
}

.invalid {
    outline: 1px solid red;
}

.validation-message {
    color: red;
}

#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

    .blazor-error-boundary::after {
        content: "An error has occurred."
    }

.loading-progress {
    position: relative;
    display: block;
    width: 8rem;
    height: 8rem;
    margin: 20vh auto 1rem auto;
}

    .loading-progress circle {
        fill: none;
        stroke: #e0e0e0;
        stroke-width: 0.6rem;
        transform-origin: 50% 50%;
        transform: rotate(-90deg);
    }

        .loading-progress circle:last-child {
            stroke: #1b6ec2;
            stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
            transition: stroke-dasharray 0.05s ease-in-out;
        }

.loading-progress-text {
    position: absolute;
    text-align: center;
    font-weight: bold;
    inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
}

    .loading-progress-text:after {
        content: var(--blazor-load-percentage-text, "Loading");
    }

code {
    color: #c02d76;
}

/* Custom Blue Button for Syncfusion Components */
.btn-blue-custom {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: white !important;
    min-width: 100px !important;
}

.btn-blue-custom:hover {
    background-color: #0b5ed7 !important;
    border-color: #0a58ca !important;
    color: white !important;
}

.btn-blue-custom:focus,
.btn-blue-custom:active {
    background-color: #0a58ca !important;
    border-color: #0a53be !important;
    color: white !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
}

.btn-blue-custom:disabled {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: white !important;
}

/* Custom Gray Cancel Button for Syncfusion Components */
.btn-cancel-custom {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: white !important;
    min-width: 100px !important;
}

    .btn-cancel-custom:hover {
        background-color: #5c636a !important;
        border-color: #565e64 !important;
        color: white !important;
    }

    .btn-cancel-custom:focus,
    .btn-cancel-custom:active {
        background-color: #565e64 !important;
        border-color: #51585e !important;
        color: white !important;
        box-shadow: 0 0 0 0.25rem rgba(108, 117, 125, 0.25) !important;
    }

    .btn-cancel-custom:disabled {
        background-color: #6c757d !important;
        border-color: #6c757d !important;
        color: white !important;
        opacity: 0.65 !important;
    }

/* Scheduler Event Template Styles */
.template-wrap {
    padding: 4px;
    height: 100%;
}

.template-wrap .subject {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 4px;
    color: #fff;
}

.template-wrap .tutor,
.template-wrap .student {
    font-size: 12px;
    color: #fff;
    margin-bottom: 2px;
}

.template-wrap .bi {
    margin-right: 4px;
}

.e-schedule .e-agenda-view .e-recurrence-icon, 
.e-schedule .e-agenda-view .e-recurrence-edit-icon {
    color: #fff !important;
}

/* Quick Info Popup Styles */
.quick-info {
    padding: 8px;
}

.quick-info .event-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.quick-info .event-details {
    font-size: 14px;
    color: #666;
}

.quick-info .event-details p {
    margin-bottom: 8px;
}

.quick-info .event-details .bi {
    margin-right: 6px;
    color: #1b6ec2;
}

/* Override default event colors */
.e-schedule .e-vertical-view .e-content-wrap .e-appointment {
    background-color: #1b6ec2 !important;
    border-color: #1861ac !important;
}

.e-schedule .e-month-view .e-appointment {
    background-color: #1b6ec2 !important;
    border-color: #1861ac !important;
}

.e-quick-popup-wrapper .e-event-popup .e-popup-header {
    background-color: white !important;
}

/* Make text in events more readable */
.e-schedule .e-vertical-view .e-content-wrap .e-appointment {
    color: white !important;
}

.e-schedule .e-month-view .e-appointment {
    color: white !important;
}

/* Style for long events that span multiple days */
.e-schedule .e-month-view .e-appointment.e-spanning {
    background-color: rgba(27, 110, 194, 0.9) !important;
}

/* Custom Event Editor Styles */
.custom-event-editor {
    padding: 20px;
}

.custom-event-editor .form-label {
    font-weight: 500;
    margin-bottom: 5px;
    color: #333;
}

.custom-event-editor .form-control,
.custom-event-editor .form-select {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
}

.custom-event-editor .form-control:focus,
.custom-event-editor .form-select:focus {
    border-color: #1b6ec2;
    box-shadow: 0 0 0 0.2rem rgba(27, 110, 194, 0.25);
}

.custom-event-editor .form-check-input {
    margin-top: 0.25rem;
}

.custom-event-editor .form-check-label {
    margin-left: 0.5rem;
}

/* Style the default Syncfusion editor popup */
.e-schedule-dialog .e-dlg-content {
    padding: 20px;
}

.e-schedule-dialog .e-field {
    margin-bottom: 15px;
}

.e-schedule-dialog .e-field label {
    font-weight: 500;
    color: #333;
}

/* Mobile-friendly styles */
@media (max-width: 991.98px) {
    .logo {
        max-width: 600px;
        height: auto;
    }
    
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    .card {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }

    .btn-sm {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    /* Grid mobile styles */
    .mobile-grid .e-grid {
        font-size: 0.875rem;
    }

    .mobile-grid .e-grid .e-gridheader {
        font-size: 0.75rem;
    }

    .mobile-grid .e-grid .e-rowcell {
        padding: 0.5rem 0.25rem;
    }

    /* Schedule mobile styles */
    .mobile-schedule .e-schedule {
        font-size: 0.875rem;
    }

    .mobile-schedule .e-schedule .e-header-cells {
        font-size: 0.75rem;
    }

    /* Mobile lesson list styles */
    .mobile-lesson-list {
        max-height: 70vh;
        overflow-y: auto;
    }

    .day-header {
        position: sticky;
        top: 0;
        z-index: 10;
        font-weight: 600;
        font-size: 0.875rem;
    }

    .lesson-card {
        border-left: 4px solid transparent;
        transition: all 0.2s ease;
    }

    .lesson-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .lesson-color {
        flex-shrink: 0;
    }

    /* Navigation mobile styles */
    .nav-text {
        margin-left: 0.5rem;
    }

    /* Custom Mobile Menu */
    .mobile-menu {
        position: fixed;
        top: 0;
        left: -300px;
        width: 280px;
        height: 100vh;
        background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
        z-index: 1009;
        transition: left 0.3s ease;
        overflow-y: auto;
    }

    .mobile-menu.show {
        left: 0;
    }

    .mobile-menu-header {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: white;
    }

    .mobile-menu-header h5 {
        margin: 0;
        color: white;
        font-size: 1.1rem;
    }

    .mobile-menu-close {
        background: none;
        border: none;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        padding: 0.25rem;
    }

    .mobile-menu-body {
        padding: 0;
    }

    .mobile-menu-body a {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        color: rgba(255,255,255,0.9);
        text-decoration: none;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        transition: all 0.2s ease;
    }

    .mobile-menu-body a:hover {
        background-color: rgba(255,255,255,0.1);
        color: white;
        text-decoration: none;
    }

    .mobile-menu-body a i,
    .mobile-menu-body a .bi {
        width: 20px;
        text-align: center;
        margin-right: 0.75rem;
    }

    .mobile-menu-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0,0,0,0.5);
        z-index: 1008;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .mobile-menu-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    /* Mobile Menu Button */
    .mobile-menu-btn.d-lg-none {
        display: flex !important;
        justify-content: center;
        align-items: center;
        background: none !important;
        border: 1px solid #dee2e6 !important;
        width: 40px !important;
        height: 40px !important;
        padding: 8px !important;
        cursor: pointer;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .mobile-menu-btn.d-lg-none:hover {
        background-color: rgba(0, 123, 255, 0.1) !important;
        border-color: #007bff !important;
        color: #007bff;
    }

    .mobile-menu-btn.d-lg-none i {
        font-size: 1.5rem;
        color: #333;
    }

    .mobile-menu-btn.d-lg-none:hover i {
        color: #007bff;
    }

    /* Remove old hamburger line styles */
    .hamburger-line {
        display: none;
    }

    /* Hide hamburger button when mobile menu is open */
    .mobile-menu.show ~ main .mobile-menu-btn {
        visibility: hidden;
    }

    /* Form mobile styles */
    .form-label {
        font-size: 0.875rem;
        font-weight: 600;
    }

    .form-control {
        font-size: 0.875rem;
    }

    /* Modal mobile styles */
    .modal-dialog {
        margin: 0.5rem;
    }

    .modal-content {
        border-radius: 0.5rem;
    }

    .modal-header {
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
    }

    .modal-body {
        padding: 1rem;
    }

    .modal-footer {
        padding: 1rem;
        border-top: 1px solid #dee2e6;
    }
}

@media (max-width: 576px) {
    .logo {
        max-width: 350px;
        height: auto;
    }

    .logo-login {
        max-width: 300px;
        height: auto;
    }
    
    .fs-3 {
        font-size: 1.5rem !important;
    }

    .fs-md-1 {
        font-size: 1.75rem !important;
    }

    .display-6 {
        font-size: 1.75rem;
    }

    .lead {
        font-size: 1rem;
    }

    /* Extra small mobile adjustments */
    .card-body {
        padding: 0.75rem;
    }

    .btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    .mobile-grid .e-grid .e-rowcell {
        padding: 0.25rem 0.125rem;
        font-size: 0.75rem;
    }

    .mobile-grid .e-grid .e-gridheader {
        font-size: 0.7rem;
        padding: 0.25rem;
    }

    /* Hide less important columns on very small screens */
    .mobile-grid .e-grid .e-hide-mobile {
        display: none !important;
    }

    /* Compact lesson cards for very small screens */
    .lesson-card .card-body {
        padding: 0.5rem;
    }

    .lesson-card h6 {
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
    }

    .lesson-card small {
        font-size: 0.75rem;
    }
}

/* Delete Confirmation Dialog Styles */
.delete-confirmation-dialog .e-dlg-container {
    border-radius: 8px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
}

.delete-confirmation-dialog .e-dlg-header-content {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
    border-radius: 8px 8px 0 0 !important;
    padding: 1rem 1.5rem !important;
}

.delete-confirmation-dialog .e-dlg-header {
    font-weight: 600 !important;
    color: #495057 !important;
    font-size: 1.1rem !important;
}

.delete-confirmation-content {
    padding: 1rem 0 !important;
}

.delete-confirmation-dialog .e-dlg-content {
    padding: 1.5rem !important;
}

.delete-confirmation-dialog .e-footer-content {
    padding: 1rem 1.5rem !important;
    border-top: 1px solid #dee2e6 !important;
    background-color: #f8f9fa !important;
    border-radius: 0 0 8px 8px !important;
    justify-content: flex-end !important;
}

/* Custom danger button for delete confirmation */
.btn-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
    min-width: 100px !important;
}

.btn-danger:hover {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
    color: white !important;
}

.btn-danger:focus,
.btn-danger:active {
    background-color: #bd2130 !important;
    border-color: #b21f2d !important;
    color: white !important;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25) !important;
}

.btn-danger:disabled {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: white !important;
    opacity: 0.65 !important;
}

/* Mobile responsiveness for delete dialog */
@media (max-width: 576px) {
    .delete-confirmation-dialog .e-dlg-container {
        margin: 1rem !important;
        width: calc(100% - 2rem) !important;
        max-width: none !important;
    }

    .delete-confirmation-dialog .e-dlg-content {
        padding: 1rem !important;
    }

    .delete-confirmation-dialog .e-footer-content {
        padding: 1rem !important;
        flex-direction: column !important;
        gap: 0.5rem !important;
    }

    .delete-confirmation-dialog .e-footer-content .e-btn {
        width: 100% !important;
        margin: 0 !important;
        margin-bottom: 10px !important;
    }
}

/* Alert Dialog Styles */
.alert-dialog .e-dlg-container {
    border-radius: 8px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
}

.alert-dialog .e-dlg-header-content {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
    border-radius: 8px 8px 0 0 !important;
    padding: 1rem 1.5rem !important;
}

.alert-dialog .e-dlg-header {
    font-weight: 600 !important;
    color: #495057 !important;
    font-size: 1.1rem !important;
}

.alert-content {
    padding: 1rem 0 !important;
}

.alert-dialog .e-dlg-content {
    padding: 1.5rem !important;
}

.alert-dialog .e-footer-content {
    padding: 1rem 1.5rem !important;
    border-top: 1px solid #dee2e6 !important;
    background-color: #f8f9fa !important;
    border-radius: 0 0 8px 8px !important;
    justify-content: center !important;
}

/* Success button styling */
.btn-success {
    background-color: #198754 !important;
    border-color: #198754 !important;
    color: white !important;
    min-width: 100px !important;
}

.btn-success:hover {
    background-color: #157347 !important;
    border-color: #146c43 !important;
    color: white !important;
}

.btn-success:focus,
.btn-success:active {
    background-color: #146c43 !important;
    border-color: #13653f !important;
    color: white !important;
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25) !important;
}

/* Warning button styling */
.btn-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #000 !important;
    min-width: 100px !important;
}

.btn-warning:hover {
    background-color: #ffca2c !important;
    border-color: #ffc720 !important;
    color: #000 !important;
}

.btn-warning:focus,
.btn-warning:active {
    background-color: #ffcd39 !important;
    border-color: #ffc720 !important;
    color: #000 !important;
    box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.25) !important;
}

/* Mobile responsiveness for alert dialog */
@media (max-width: 576px) {
    .alert-dialog .e-dlg-container {
        margin: 1rem !important;
        width: calc(100% - 2rem) !important;
        max-width: none !important;
    }

    .alert-dialog .e-dlg-content {
        padding: 1rem !important;
    }

    .alert-dialog .e-footer-content {
        padding: 1rem !important;
    }
}
